import React from 'react';
import { Link } from 'react-router-dom';

const Footer: React.FC = () => {
    const navLinks = [
        { name: 'Accueil', path: '/' },
        { name: 'À Propos', path: '/a-propos' },
        { name: 'Compétences', path: '/competences' },
        { name: 'Portfolio', path: '/portfolio' },
        { name: 'Assistance', path: '/assistance' },
        { name: 'Contact', path: '/contact' },
    ];

    return (
        <footer className="bg-brand-dark border-t border-brand-surface mt-20">
            <div className="container mx-auto px-6 py-12">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <div>
                        <h3 className="text-xl font-bold text-white mb-4">Flexo<span className="text-brand-blue">Div</span></h3>
                        <p className="text-brand-muted max-w-xs">
                            Spécialiste en ingénierie du prompt et développement créatif. Je transforme vos idées en expériences numériques innovantes.
                        </p>
                    </div>
                    <div>
                        <h4 className="font-semibold text-white mb-4">Navigation</h4>
                        <ul className="space-y-2">
                            {navLinks.map(link => (
                                <li key={link.name}>
                                    <Link to={link.path} className="text-brand-muted hover:text-brand-purple transition-colors">{link.name}</Link>
                                </li>
                            ))}
                        </ul>
                    </div>
                    <div>
                        <h4 className="font-semibold text-white mb-4">Compétences</h4>
                         <ul className="space-y-2">
                             <li><span className="text-brand-muted">Ingénierie Prompt</span></li>
                             <li><span className="text-brand-muted">Développement Créatif</span></li>
                             <li><span className="text-brand-muted">Assistant IA Intégré</span></li>
                             <li><span className="text-brand-muted">Animations GSAP</span></li>
                         </ul>
                    </div>
                    <div>
                        <h4 className="font-semibold text-white mb-4">Ressources</h4>
                        <ul className="space-y-2">
                            <li><Link to="/portfolio" className="text-brand-muted hover:text-brand-blue transition-colors">Portfolio</Link></li>
                            <li><Link to="/assistance" className="text-brand-muted hover:text-brand-blue transition-colors">Assistance</Link></li>
                            <li><Link to="/contact" className="text-brand-muted hover:text-brand-blue transition-colors">Contact</Link></li>
                            <li><a href="https://flexodiv.netlify.app" target="_blank" rel="noopener noreferrer" className="text-brand-muted hover:text-brand-blue transition-colors">Site Web</a></li>
                        </ul>
                    </div>
                </div>
                <div className="mt-12 border-t border-brand-surface pt-8 flex flex-col md:flex-row justify-between items-center text-sm text-brand-muted">
                    <p>&copy; {new Date().getFullYear()} FlexoDiv - Francisco. Tous droits réservés.</p>

                    {/* Section des icônes sociales */}
                    <div className="flex justify-center items-center space-x-6 mt-4 md:mt-0">

                        {/* Lien Email */}
                        <a href="mailto:<EMAIL>?subject=Prise%20de%20contact"
                           className="text-brand-muted hover:text-brand-blue transition-colors duration-300"
                           aria-label="Envoyer un email">
                            <span className="sr-only">Email</span>
                            <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6zm-2 0l-8 5-8-5h16zm0 12H4V8l8 5 8-5v10z"/>
                            </svg>
                        </a>

                        {/* Lien LinkedIn */}
                        <a href="https://www.linkedin.com/in/flexodiv-développeur-de-solutions-ia-982582203"
                           target="_blank"
                           rel="noopener noreferrer"
                           className="text-brand-muted hover:text-brand-blue transition-colors duration-300"
                           aria-label="Voir le profil LinkedIn">
                            <span className="sr-only">LinkedIn</span>
                            <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                            </svg>
                        </a>

                        {/* Lien YouTube */}
                        <a href="https://www.youtube.com/@flexodiv"
                           target="_blank"
                           rel="noopener noreferrer"
                           className="text-brand-muted hover:text-brand-blue transition-colors duration-300"
                           aria-label="Visiter la chaîne YouTube">
                            <span className="sr-only">YouTube</span>
                            <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>

                        {/* Lien GitHub */}
                        <a href="https://github.com/cisco-03"
                           target="_blank"
                           rel="noopener noreferrer"
                           className="text-brand-muted hover:text-brand-blue transition-colors duration-300"
                           aria-label="Voir le profil GitHub">
                            <span className="sr-only">GitHub</span>
                            <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
